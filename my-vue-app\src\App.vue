<script setup>
import { ref } from 'vue'
import LoginPage from './components/LoginPage.vue'
import SmsLoginPage from './components/SmsLoginPage.vue'
import RoleSelectionPage from './components/RoleSelectionPage.vue'
import SuccessPage from './components/SuccessPage.vue'
import HomePage from './components/HomePage.vue'
import EssayRequirements from './components/EssayRequirements.vue'
import EssayDetail from './components/EssayDetail.vue'
import ProfilePage from './components/ProfilePage.vue'
import HistoryPage from './components/HistoryPage.vue'


const currentPage = ref('login') // 'login', 'sms-login', 'role-selection', 'success', 'home', 'essay-requirements', 'essay-detail', 'profile', 'history'

const showSmsLogin = () => {
  currentPage.value = 'sms-login'
}

const showLogin = () => {
  currentPage.value = 'login'
}

const showRoleSelection = () => {
  currentPage.value = 'role-selection'
}

const showSuccess = () => {
  currentPage.value = 'success'
}

const showHome = () => {
  currentPage.value = 'home'
}

const showEssayRequirements = () => {
  currentPage.value = 'essay-requirements'
}

const showEssayDetail = () => {
  currentPage.value = 'essay-detail'
}

const handleRoleSelected = (role) => {
  console.log('用户选择的身份:', role)
  // 显示成功页面，然后跳转到首页
  showSuccess()
  setTimeout(() => {
    showHome()
  }, 2000) // 2秒后自动跳转到首页
}

const handleReturn = () => {
  // 从成功页面返回到登录页面
  showLogin()
}

const handleUploadEssay = (essayId) => {
  console.log('跳转到作文要求页面，作文ID:', essayId)
  showEssayRequirements()
}

const handleGoBackFromEssay = () => {
  showHome()
}

const handleEssayClick = (essayId) => {
  console.log('点击作文详情，作文ID:', essayId)
  showEssayDetail()
}

const handleGoBackFromDetail = () => {
  showHome()
}

const showProfile = () => {
  currentPage.value = 'profile'
}

const showHistory = () => {
  currentPage.value = 'history'
}

const handleTabSwitch = (tab) => {
  console.log('切换到标签页:', tab)
  switch (tab) {
    case 'home':
      showHome()
      break
    case 'profile':
      showProfile()
      break
    case 'history':
      showHistory()
      break
    case 'data':
      console.log('跳转到成长数据页面')
      break
    default:
      console.log('未知标签页:', tab)
  }
}

const handleMenuClick = (action) => {
  console.log('菜单点击:', action)
  switch (action) {
    case 'message':
      console.log('跳转到消息中心')
      break
    case 'feedback':
      console.log('跳转到意见反馈')
      break
    case 'switch':
      console.log('切换身份')
      break
    case 'logout':
      console.log('退出登录')
      showLogin()
      break
    default:
      console.log('未知菜单操作:', action)
  }
}
</script>

<template>
  <div id="app">
    <LoginPage
      v-if="currentPage === 'login'"
      @sms-login="showSmsLogin"
    />
    <SmsLoginPage
      v-if="currentPage === 'sms-login'"
      @back="showLogin"
      @login-success="showRoleSelection"
    />
    <RoleSelectionPage
      v-if="currentPage === 'role-selection'"
      @back="showSmsLogin"
      @role-selected="handleRoleSelected"
    />
    <SuccessPage
      v-if="currentPage === 'success'"
      @return="handleReturn"
    />
    <HomePage
      v-if="currentPage === 'home'"
      @upload-essay="handleUploadEssay"
      @essay-click="handleEssayClick"
      @switch-tab="handleTabSwitch"
    />
    <EssayRequirements
      v-if="currentPage === 'essay-requirements'"
      @go-back="handleGoBackFromEssay"
    />
    <EssayDetail
      v-if="currentPage === 'essay-detail'"
      @go-back="handleGoBackFromDetail"
    />
    <ProfilePage
      v-if="currentPage === 'profile'"
      @switch-tab="handleTabSwitch"
      @menu-click="handleMenuClick"
    />
    <HistoryPage
      v-if="currentPage === 'history'"
      @switch-tab="handleTabSwitch"
    />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

#app {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}
</style>
