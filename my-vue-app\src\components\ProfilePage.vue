<template>
  <div class="profile-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-container">
          <img :src="signalIcon" alt="信号" class="signal-icon" />
          <img :src="signalBar1" alt="信号条1" class="signal-bar" />
          <img :src="signalBar2" alt="信号条2" class="signal-bar" />
          <img :src="signalBar3" alt="信号条3" class="signal-bar" />
        </div>
      </div>
      <div class="status-right">
        <div class="battery-container">
          <img :src="batteryOutline" alt="电池外框" class="battery-outline" />
          <img :src="batteryFill2" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill3" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill4" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill5" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill6" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill7" alt="电池电量" class="battery-fill" />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 用户头像和信息 -->
      <div class="user-section">
        <div class="user-avatar">
          <img :src="avatarMain" alt="Rachel" />
        </div>
        <div class="user-info">
          <div class="user-name">Rachel</div>
          <div class="edit-info">编辑个人信息</div>
        </div>
        <div class="caret-right">
          <img :src="caretRight" alt="右箭头" />
        </div>
      </div>

      <!-- 身份标签 -->
      <div class="role-tag">
        <span>妈妈</span>
      </div>

      <!-- 家庭成员区域 -->
      <div class="family-section">
        <div class="section-title">家庭成员（4）</div>
        <div class="family-members">
          <!-- 孩子1 -->
          <div class="member-item">
            <div class="member-avatar">
              <img :src="child1Avatar" alt="孩子1" />
            </div>
            <div class="member-name">孩子1</div>
          </div>

          <!-- 孩子2 -->
          <div class="member-item">
            <div class="member-avatar child2">
              <img :src="child2Avatar" alt="孩子2" />
            </div>
            <div class="member-name">孩子2</div>
          </div>

          <!-- 孩子3 -->
          <div class="member-item">
            <div class="member-avatar child3">
              <img :src="child3Avatar" alt="孩子3" />
            </div>
            <div class="member-name">孩子3</div>
          </div>

          <!-- 爸爸 -->
          <div class="member-item">
            <div class="member-avatar father">
              <img :src="fatherAvatar" alt="爸爸" />
            </div>
            <div class="member-name">爸爸</div>
          </div>

          <!-- 添加成员按钮 -->
          <div class="add-member">
            <div class="add-member-btn">
              <div class="add-vertical"></div>
              <div class="add-horizontal"></div>
            </div>
            <div class="member-name">成员</div>
          </div>
        </div>
      </div>

      <!-- 菜单项区域 -->
      <div class="menu-section">
        <div class="menu-item" @click="handleMenuClick('message')">
          <span class="menu-text">消息中心</span>
          <img :src="messageCaretRight" alt="右箭头" class="menu-arrow" />
        </div>

        <div class="menu-item" @click="handleMenuClick('feedback')">
          <span class="menu-text">意见反馈</span>
          <img :src="feedbackCaretRight" alt="右箭头" class="menu-arrow" />
        </div>

        <div class="menu-item version-item">
          <span class="menu-text">版本号</span>
          <span class="version-text">v0.1</span>
        </div>

        <div class="menu-item" @click="handleMenuClick('switch')">
          <span class="menu-text">切换身份</span>
          <img :src="switchCaretRight" alt="右箭头" class="menu-arrow" />
        </div>
      </div>

      <!-- 退出登录区域 -->
      <div class="logout-section">
        <div class="menu-item" @click="handleMenuClick('logout')">
          <span class="menu-text">退出登录</span>
          <img :src="logoutCaretRight" alt="右箭头" class="menu-arrow" />
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item" @click="switchTab('home')">
        <img :src="homeIcon" alt="首页" class="tab-icon" />
        <span class="tab-text">首页</span>
      </div>

      <div class="tab-item" @click="switchTab('history')">
        <img :src="folderIcon" alt="过往作文" class="tab-icon" />
        <span class="tab-text">过往作文</span>
      </div>

      <div class="tab-item" @click="switchTab('data')">
        <img :src="assessmentIcon" alt="成长数据" class="tab-icon" />
        <span class="tab-text">成长数据</span>
      </div>

      <div class="tab-item active" @click="switchTab('profile')">
        <img :src="userIcon" alt="个人中心" class="tab-icon" />
        <span class="tab-text">个人中心</span>
      </div>
    </div>
  </div>
</template>

<script>
// 导入所有图片资源
import signalIcon from '../assets/profile/signal-icon.svg'
import signalBar1 from '../assets/profile/signal-bar1.svg'
import signalBar2 from '../assets/profile/signal-bar2.svg'
import signalBar3 from '../assets/profile/signal-bar3.svg'
import batteryOutline from '../assets/profile/battery-outline.svg'
import batteryFill1 from '../assets/profile/battery-fill1.svg'
import batteryFill2 from '../assets/profile/battery-fill2.svg'
import batteryFill3 from '../assets/profile/battery-fill3.svg'
import batteryFill4 from '../assets/profile/battery-fill4.svg'
import batteryFill5 from '../assets/profile/battery-fill5.svg'
import batteryFill6 from '../assets/profile/battery-fill6.svg'
import batteryFill7 from '../assets/profile/battery-fill7.svg'
import avatarMain from '../assets/profile/avatar-main-56586a.png'
import caretRight from '../assets/profile/caret-right.svg'
import child1Avatar from '../assets/profile/child1-avatar-56586a.png'
import child2Avatar from '../assets/profile/child2-avatar-56586a.png'
import child3Avatar from '../assets/profile/child3-avatar-56586a.png'
import fatherAvatar from '../assets/profile/father-avatar-56586a.png'
import addIconVertical from '../assets/profile/add-icon-vertical.svg'
import addIconHorizontal from '../assets/profile/add-icon-horizontal.svg'
import messageCaretRight from '../assets/profile/message-caret-right.svg'
import feedbackCaretRight from '../assets/profile/feedback-caret-right.svg'
import switchCaretRight from '../assets/profile/switch-caret-right.svg'
import logoutCaretRight from '../assets/profile/logout-caret-right.svg'
import homeIcon from '../assets/images/home-icon-figma.svg'
import folderIcon from '../assets/images/folder-icon-figma.svg'
import assessmentIcon from '../assets/images/assessment-icon-figma.svg'
import userIcon from '../assets/images/user-icon-figma.svg'

export default {
  name: 'ProfilePage',
  emits: ['switch-tab', 'menu-click'],
  data() {
    return {
      // 状态栏图标
      signalIcon,
      signalBar1,
      signalBar2,
      signalBar3,
      batteryOutline,
      batteryFill1,
      batteryFill2,
      batteryFill3,
      batteryFill4,
      batteryFill5,
      batteryFill6,
      batteryFill7,
      // 用户头像
      avatarMain,
      caretRight,
      // 家庭成员头像
      child1Avatar,
      child2Avatar,
      child3Avatar,
      fatherAvatar,
      // 添加图标
      addIconVertical,
      addIconHorizontal,
      // 菜单箭头
      messageCaretRight,
      feedbackCaretRight,
      switchCaretRight,
      logoutCaretRight,
      // 底部导航图标
      homeIcon,
      folderIcon,
      assessmentIcon,
      userIcon
    }
  },
  methods: {
    switchTab(tab) {
      this.$emit('switch-tab', tab)
    },
    handleMenuClick(action) {
      this.$emit('menu-click', action)
    }
  }
}
</script>

<style scoped>
.profile-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-container {
  position: relative;
  width: 27.34px;
  height: 10.7px;
}

.signal-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 7.86px;
  height: 10.7px;
}

.signal-bar {
  position: absolute;
}

.signal-bar:nth-child(2) {
  left: 9.59px;
  top: 1.61px;
  width: 2.25px;
  height: 7.47px;
}

.signal-bar:nth-child(3) {
  left: 13.53px;
  top: 0.25px;
  width: 8.1px;
  height: 10.19px;
}

.signal-bar:nth-child(4) {
  left: 22.86px;
  top: 0.25px;
  width: 4.48px;
  height: 10.19px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-container {
  position: relative;
  width: 65.87px;
  height: 10.56px;
}

.battery-outline {
  position: absolute;
  left: 43.05px;
  top: 0;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
}

.battery-fill {
  position: absolute;
}

.battery-fill:nth-child(3) {
  left: 44.26px;
  top: 1.2px;
  width: 17.87px;
  height: 7.66px;
}

/* 主要内容区域 */
.main-content {
  padding-top: 28px;
}

/* 用户信息区域 */
.user-section {
  display: flex;
  align-items: center;
  padding: 0 21px 0 21px;
  margin-bottom: 17px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  border: 1px solid #636AE8;
  background: #CED0F8;
  overflow: hidden;
  margin-right: 28px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #171A1F;
  margin-bottom: 0;
}

.edit-info {
  font-family: Inter;
  font-weight: 400;
  font-size: 8px;
  line-height: 14px;
  color: #BCC1CA;
  margin-top: 0;
}

.caret-right {
  width: 12px;
  height: 12px;
}

.caret-right img {
  width: 100%;
  height: 100%;
}

/* 身份标签 */
.role-tag {
  margin: 0 129px 32px 129px;
  width: 39px;
  height: 18px;
  background: #F2F2FD;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.role-tag span {
  font-family: Inter;
  font-weight: 400;
  font-size: 8px;
  line-height: 14px;
  color: #636AE8;
}

/* 家庭成员区域 */
.family-section {
  background: #F8F9FA;
  border-radius: 10px;
  margin: 0 20px 28px 20px;
  padding: 10px 16px 24px 16px;
}

.section-title {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #171A1F;
  margin-bottom: 20px;
}

.family-members {
  display: flex;
  gap: 18px;
  align-items: flex-start;
  position: relative;
}

.member-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.member-avatar {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  border: 2px solid #7F55E0;
  background: #D8CBF5;
  overflow: hidden;
  margin-bottom: 10px;
}

.member-avatar.child2 {
  border-color: #22CCB2;
  background: #BAF3EB;
}

.member-avatar.child3 {
  border-color: #E8618C;
  background: #F8CEDB;
}

.member-avatar.father {
  border-color: #E8618C;
  background: #F8CEDB;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-name {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #323842;
  text-align: center;
}

.add-member {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
}

.add-member-btn {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  border: 1px dashed #7F55E0;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 10px;
  box-shadow: 0px 0px 1px 0px rgba(23, 26, 31, 0.07), 0px 0px 2px 0px rgba(23, 26, 31, 0.12);
}

.add-vertical {
  position: absolute;
  width: 2px;
  height: 17.2px;
  background: #7F55E0;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.add-horizontal {
  position: absolute;
  width: 17.2px;
  height: 2px;
  background: #7F55E0;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 菜单区域 */
.menu-section {
  background: #F8F9FA;
  border-radius: 10px;
  margin: 0 20px 14px 20px;
  padding: 8px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  background: #F8F9FA;
  border-radius: 6px;
  margin: 0;
  cursor: pointer;
}

.menu-item:hover {
  background: #F0F1F3;
}

.menu-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.menu-arrow {
  width: 16px;
  height: 16px;
}

.version-item {
  cursor: default;
}

.version-item:hover {
  background: #F8F9FA;
}

.version-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

/* 退出登录区域 */
.logout-section {
  background: #F8F9FA;
  border-radius: 10px;
  margin: 0 20px 54px 20px;
  padding: 8px 0;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 375px;
  height: 48px;
  background: #FFFFFF;
  border-top: 1px solid #F3F4F6;
  display: flex;
  padding: 0 6px;
  box-sizing: border-box;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.tab-text {
  font-size: 10px;
  line-height: 16px;
  color: #424955;
}

.tab-item.active .tab-text {
  font-weight: 700;
  color: #4850E4;
}


</style>
