<template>
  <div class="login-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- Logo -->
      <div class="logo-container">
        <img src="../assets/images/logo.svg" alt="慧习作" class="logo" />
      </div>

      <!-- 欢迎文字 -->
      <div class="welcome-text">欢迎使用慧习作</div>

      <!-- 登录按钮组 -->
      <div class="login-buttons">
        <!-- 微信授权登录 -->
        <button class="login-btn wechat-btn" @click="handleWechatLogin">
          <img src="../assets/images/wechat-icon.svg" alt="微信" class="btn-icon" />
          <span class="btn-text">授权登录</span>
        </button>

        <!-- 手机验证码登录 -->
        <button class="login-btn sms-btn" @click="handleSmsLogin">
          <img src="../assets/images/phone-icon.svg" alt="手机" class="btn-icon" />
          <span class="btn-text">手机验证码</span>
        </button>

        <!-- 手机密码登录 -->
        <button class="login-btn password-btn" @click="handlePasswordLogin">
          <img src="../assets/images/lock-icon.svg" alt="密码" class="btn-icon" />
          <span class="btn-text">手机密码</span>
        </button>
      </div>
    </div>

    <!-- 底部指示器 -->
    <div class="bottom-indicator"></div>
  </div>
</template>

<script>
export default {
  name: 'LoginPage',
  emits: ['sms-login'],
  methods: {
    handleWechatLogin() {
      console.log('微信授权登录');
      // 这里添加微信登录逻辑
    },
    handleSmsLogin() {
      console.log('手机验证码登录');
      this.$emit('sms-login');
    },
    handlePasswordLogin() {
      console.log('手机密码登录');
      // 这里添加密码登录逻辑
    }
  }
}
</script>

<style scoped>
.login-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: #FFFFFF;
  border-bottom: 1px solid #F3F4F6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #171A1F;
}

.bar:nth-child(1) {
  height: 3.4px;
}

.bar:nth-child(2) {
  height: 5.53px;
}

.bar:nth-child(3) {
  height: 8.51px;
}

.bar:nth-child(4) {
  height: 10.21px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #171A1F;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #171A1F;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 主要内容 */
.main-content {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Logo */
.logo-container {
  margin-top: 263px;
  margin-bottom: 24px;
}

.logo {
  width: 199px;
  height: 40px;
}

/* 欢迎文字 */
.welcome-text {
  font-size: 12px;
  line-height: 20px;
  color: #9095A0;
  text-align: center;
  margin-bottom: 200px;
}

/* 登录按钮组 */
.login-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.login-btn {
  width: 350px;
  height: 52px;
  border-radius: 26px;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.btn-icon {
  width: 24px;
  height: 24px;
}

.btn-text {
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  font-weight: 400;
}

/* 微信登录按钮 */
.wechat-btn {
  background: #379AE6;
  box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
}

.wechat-btn:hover {
  background: #2d8bd4;
  transform: translateY(-1px);
}

/* 手机验证码登录按钮 */
.sms-btn {
  background: #636AE8;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
}

.sms-btn:hover {
  background: #5258d6;
  transform: translateY(-1px);
}

/* 手机密码登录按钮 */
.password-btn {
  background: #7F55E0;
  box-shadow: 0px 4px 9px 0px rgba(127, 85, 224, 0.11), 0px 0px 2px 0px rgba(127, 85, 224, 0.12);
}

.password-btn:hover {
  background: #6d43ce;
  transform: translateY(-1px);
}

/* 底部指示器 */
.bottom-indicator {
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
  width: 160px;
  height: 5px;
  background: #171A1F;
  border: 1px solid #BCC1CA;
  border-radius: 3px;
}
</style>
