<template>
  <div class="success-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 成功图标 -->
      <div class="success-icon-container">
        <img src="../assets/images/success-icon.svg" alt="成功" class="success-icon" />
      </div>

      <!-- 成功文字 -->
      <div class="success-text">成功</div>

      <!-- 返回按钮 -->
      <button class="return-button" @click="handleReturn">
        返回
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SuccessPage',
  emits: ['return'],
  methods: {
    handleReturn() {
      console.log('返回');
      this.$emit('return');
    }
  }
}
</script>

<style scoped>
.success-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  border-bottom: 1px solid #F3F4F6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #171A1F;
}

.bar:nth-child(1) {
  height: 3.4px;
}

.bar:nth-child(2) {
  height: 5.53px;
}

.bar:nth-child(3) {
  height: 8.51px;
}

.bar:nth-child(4) {
  height: 10.21px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #171A1F;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #171A1F;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 主要内容 */
.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20px;
  height: calc(100% - 40px);
  justify-content: center;
  position: relative;
}

/* 成功图标 */
.success-icon-container {
  margin-bottom: 16px;
}

.success-icon {
  width: 80px;
  height: 80px;
}

/* 成功文字 */
.success-text {
  font-family: 'Archivo', sans-serif;
  font-weight: 700;
  font-size: 22px;
  line-height: 34px;
  color: #171A1F;
  text-align: center;
  margin-bottom: 339px;
}

/* 返回按钮 */
.return-button {
  position: absolute;
  bottom: 46px;
  left: 20px;
  right: 20px;
  width: 350px;
  height: 52px;
  background: #636AE8;
  border: 1px solid transparent;
  border-radius: 16px;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.return-button:hover {
  background: #5258d6;
  transform: translateY(-1px);
}
</style>
