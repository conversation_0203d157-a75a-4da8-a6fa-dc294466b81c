<template>
  <div class="role-selection-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 返回按钮 -->
      <button class="back-button" @click="goBack">
        <img src="../assets/images/arrow-left.svg" alt="返回" class="back-icon" />
      </button>

      <!-- 标题 -->
      <div class="title-section">
        <h2 class="page-title">请选择身份</h2>
      </div>

      <!-- 身份选择按钮 -->
      <div class="role-buttons">
        <!-- 教师按钮 -->
        <button 
          class="role-btn teacher-btn" 
          :class="{ active: selectedRole === 'teacher' }"
          @click="selectRole('teacher')"
        >
          <img src="../assets/images/teacher-face.svg" alt="教师" class="role-icon" />
          <span class="role-text">教师</span>
        </button>

        <!-- 家长按钮 -->
        <button 
          class="role-btn parent-btn" 
          :class="{ active: selectedRole === 'parent' }"
          @click="selectRole('parent')"
        >
          <img src="../assets/images/parent-face.svg" alt="家长" class="role-icon" />
          <span class="role-text">家长</span>
        </button>
      </div>

      <!-- 确定按钮 -->
      <button class="confirm-button" @click="handleConfirm" :disabled="!selectedRole">
        确定
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RoleSelectionPage',
  emits: ['back', 'role-selected'],
  data() {
    return {
      selectedRole: 'parent' // 默认选择家长
    }
  },
  methods: {
    goBack() {
      console.log('返回上一页');
      this.$emit('back');
    },
    selectRole(role) {
      console.log('选择身份:', role);
      this.selectedRole = role;
    },
    handleConfirm() {
      if (this.selectedRole) {
        console.log('确认选择身份:', this.selectedRole);
        this.$emit('role-selected', this.selectedRole);
      }
    }
  }
}
</script>

<style scoped>
.role-selection-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  border-bottom: 1px solid #F3F4F6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #171A1F;
}

.bar:nth-child(1) {
  height: 3.4px;
}

.bar:nth-child(2) {
  height: 5.53px;
}

.bar:nth-child(3) {
  height: 8.51px;
}

.bar:nth-child(4) {
  height: 10.21px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #171A1F;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #171A1F;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 主要内容 */
.main-content {
  padding: 12px 33px 0;
}

/* 返回按钮 */
.back-button {
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-bottom: 97px;
}

.back-icon {
  width: 24px;
  height: 24px;
}

/* 标题区域 */
.title-section {
  margin-bottom: 133px;
}

.page-title {
  font-family: 'Archivo', sans-serif;
  font-weight: 400;
  font-size: 20px;
  line-height: 30px;
  color: #9095A0;
  margin: 0;
}

/* 身份选择按钮 */
.role-buttons {
  display: flex;
  flex-direction: column;
  gap: 31px;
  margin-bottom: 275px;
  align-items: center;
}

.role-btn {
  width: 248px;
  height: 52px;
  border-radius: 26px;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.role-icon {
  width: 24px;
  height: 24px;
}

.role-text {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
}

/* 教师按钮 */
.teacher-btn {
  background: #379AE6;
  box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
}

.teacher-btn .role-text {
  color: #FFFFFF;
}

.teacher-btn:hover {
  background: #2d8bd4;
  transform: translateY(-1px);
}

/* 家长按钮 */
.parent-btn {
  background: #FFFFFF;
  border: 1px solid #379AE6;
  box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
}

.parent-btn .role-text {
  color: #379AE6;
}

.parent-btn:hover {
  background: #f8fbff;
  transform: translateY(-1px);
}

/* 激活状态 */
.role-btn.active {
  transform: scale(1.02);
}

/* 确定按钮 */
.confirm-button {
  width: 350px;
  height: 52px;
  background: #636AE8;
  border: 1px solid transparent;
  border-radius: 16px;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-button:hover:not(:disabled) {
  background: #5258d6;
  transform: translateY(-1px);
}

.confirm-button:disabled {
  background: #BCC1CA;
  cursor: not-allowed;
}
</style>
