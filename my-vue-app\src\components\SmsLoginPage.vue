<template>
  <div class="sms-login-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 返回按钮 -->
      <button class="back-button" @click="goBack">
        <img src="../assets/images/arrow-left.svg" alt="返回" class="back-icon" />
      </button>

      <!-- 标题区域 -->
      <div class="title-section">
        <h1 class="main-title">Hello!</h1>
        <p class="subtitle">请使用手机验证码登录</p>
      </div>

      <!-- 表单区域 -->
      <div class="form-section">
        <!-- 手机号码输入 -->
        <div class="input-group">
          <label class="input-label">手机号码</label>
          <div class="input-field">
            <input 
              type="tel" 
              v-model="phoneNumber" 
              placeholder="13860888888"
              class="text-input"
            />
          </div>
        </div>

        <!-- 验证码输入 -->
        <div class="input-group">
          <label class="input-label">密码</label>
          <div class="input-field verification-field">
            <input 
              type="text" 
              v-model="verificationCode" 
              placeholder="请输入验证码"
              class="text-input"
            />
            <div class="divider-line"></div>
            <button class="send-code-btn" @click="sendVerificationCode">
              发送验证码
            </button>
          </div>
        </div>
      </div>

      <!-- 登录按钮 -->
      <button class="login-button" @click="handleLogin">
        登录
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmsLoginPage',
  emits: ['back', 'login-success'],
  data() {
    return {
      phoneNumber: '13860888888',
      verificationCode: ''
    }
  },
  methods: {
    goBack() {
      console.log('返回上一页');
      this.$emit('back');
    },
    sendVerificationCode() {
      console.log('发送验证码到:', this.phoneNumber);
      // 这里添加发送验证码逻辑
      alert('验证码已发送');
    },
    handleLogin() {
      console.log('登录', {
        phone: this.phoneNumber,
        code: this.verificationCode
      });

      if (!this.verificationCode) {
        alert('请输入验证码');
        return;
      }

      // 模拟登录成功
      console.log('登录成功');
      this.$emit('login-success');
    }
  }
}
</script>

<style scoped>
.sms-login-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  border-bottom: 1px solid #F3F4F6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #171A1F;
}

.bar:nth-child(1) {
  height: 3.4px;
}

.bar:nth-child(2) {
  height: 5.53px;
}

.bar:nth-child(3) {
  height: 8.51px;
}

.bar:nth-child(4) {
  height: 10.21px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #171A1F;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #171A1F;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 主要内容 */
.main-content {
  padding: 12px 33px 0;
}

/* 返回按钮 */
.back-button {
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-bottom: 46px;
}

.back-icon {
  width: 24px;
  height: 24px;
}

/* 标题区域 */
.title-section {
  margin-bottom: 54px;
}

.main-title {
  font-family: 'Archivo', sans-serif;
  font-weight: 700;
  font-size: 32px;
  line-height: 48px;
  color: #171A1F;
  margin: 0 0 0 0;
}

.subtitle {
  font-family: 'Archivo', sans-serif;
  font-weight: 400;
  font-size: 20px;
  line-height: 30px;
  color: #9095A0;
  margin: 0;
}

/* 表单区域 */
.form-section {
  margin-bottom: 364px;
}

.input-group {
  margin-bottom: 16px;
}

.input-label {
  display: block;
  font-weight: 700;
  font-size: 16px;
  line-height: 26px;
  color: #424955;
  margin-bottom: 1px;
}

.input-field {
  position: relative;
}

.text-input {
  width: 100%;
  height: 43px;
  background: #F3F4F6;
  border: 1px solid transparent;
  border-radius: 6px;
  padding: 9px 16px;
  font-size: 16px;
  line-height: 26px;
  color: #171A1F;
  box-sizing: border-box;
}

.text-input::placeholder {
  color: #BCC1CA;
}

.text-input:focus {
  outline: none;
  border-color: #636AE8;
}

/* 验证码字段特殊样式 */
.verification-field {
  display: flex;
  align-items: center;
  position: relative;
}

.verification-field .text-input {
  width: 323px;
  padding-right: 90px;
}

.divider-line {
  position: absolute;
  right: 82px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 18px;
  background: #BCC1CA;
}

.send-code-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  font-size: 14px;
  line-height: 22px;
  color: #636AE8;
  cursor: pointer;
  padding: 0;
}

.send-code-btn:hover {
  color: #5258d6;
}

/* 登录按钮 */
.login-button {
  width: 350px;
  height: 52px;
  background: #636AE8;
  border: 1px solid transparent;
  border-radius: 16px;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-button:hover {
  background: #5258d6;
  transform: translateY(-1px);
}
</style>
