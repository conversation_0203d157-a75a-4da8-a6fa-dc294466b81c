<template>
  <div class="history-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="search-container">
          <img :src="searchIcon" alt="搜索" class="search-icon" />
          <img :src="searchLine" alt="搜索线" class="search-line" />
          <img :src="searchText" alt="搜索文本" class="search-text" />
          <img :src="searchButton" alt="搜索按钮" class="search-button" />
        </div>
      </div>
      <div class="status-right">
        <div class="filter-container">
          <img :src="filterIcon" alt="筛选" class="filter-icon" />
          <img :src="filterDot" alt="筛选点" class="filter-dot" />
          <img :src="filterMain" alt="筛选主体" class="filter-main" />
          <img :src="filterElement1" alt="筛选元素1" class="filter-element1" />
          <img :src="filterElement2" alt="筛选元素2" class="filter-element2" />
          <img :src="filterElement3" alt="筛选元素3" class="filter-element3" />
          <img :src="filterElement4" alt="筛选元素4" class="filter-element4" />
          <img :src="filterElement5" alt="筛选元素5" class="filter-element5" />
        </div>
      </div>
    </div>

    <!-- 筛选器区域 -->
    <div class="filter-section">
      <!-- 学年筛选 -->
      <div class="dropdown-button">
        <span class="dropdown-text">2025～2026学年 上学期</span>
        <img :src="chevronDown1" alt="下拉" class="chevron-icon" />
      </div>

      <!-- 单元作文筛选 -->
      <div class="dropdown-button simple-style">
        <span class="dropdown-text">单元作文</span>
        <img :src="chevronDown2" alt="下拉" class="chevron-icon" />
      </div>

      <!-- 记叙文筛选 -->
      <div class="dropdown-button">
        <span class="dropdown-text">记叙文</span>
        <img :src="chevronDown3" alt="下拉" class="chevron-icon" />
      </div>
    </div>

    <!-- 作文列表 -->
    <div class="essay-list-container">
      <!-- 学年标题 -->
      <div class="academic-year-header">
        <div class="year-title">2025～2026学年 上学期</div>
        <div class="essay-count">共8篇</div>
      </div>

      <!-- 第二单元 -->
      <div class="essay-group">
        <div class="essay-item">
          <div class="essay-content">
            <div class="essay-tags">
              <span class="unit-tag">第二单元</span>
              <span class="type-tag">单元作文</span>
              <span class="category-tag">全命题</span>
            </div>
            <div class="essay-title">我的植物朋友</div>
            <div class="essay-time">2025.09.08 11:22</div>
          </div>
          <div class="essay-actions">
            <div class="essay-score">
              <span class="score-number">28</span>
              <span class="score-unit">分</span>
            </div>
            <div class="arrow-icon">
              <img :src="caretRight1" alt="查看" />
            </div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="separator"></div>

      <!-- 第一单元 -->
      <div class="essay-group">
        <div class="essay-item">
          <div class="essay-content">
            <div class="essay-tags">
              <span class="unit-tag">第一单元</span>
              <span class="type-tag">单元作文</span>
              <span class="category-tag">全命题</span>
            </div>
            <div class="essay-title">我的植物朋友</div>
            <div class="essay-time">2025.09.08 11:22</div>
          </div>
          <div class="essay-actions">
            <div class="essay-score">
              <span class="score-number">28</span>
              <span class="score-unit">分</span>
            </div>
            <div class="arrow-icon">
              <img :src="caretRight2" alt="查看" />
            </div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="separator"></div>
    </div>

    <!-- 底线 -->
    <div class="bottom-line">------我是有底线的------</div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item" @click="switchTab('home')">
        <img :src="homeIcon" alt="首页" class="tab-icon" />
        <span class="tab-text">首页</span>
      </div>

      <div class="tab-item active" @click="switchTab('history')">
        <img :src="folderIcon" alt="过往作文" class="tab-icon" />
        <span class="tab-text">过往作文</span>
      </div>

      <div class="tab-item" @click="switchTab('data')">
        <img :src="assessmentIcon" alt="成长数据" class="tab-icon" />
        <span class="tab-text">成长数据</span>
      </div>

      <div class="tab-item" @click="switchTab('profile')">
        <img :src="userIcon" alt="个人中心" class="tab-icon" />
        <span class="tab-text">个人中心</span>
      </div>
    </div>
  </div>
</template>

<script>
// 导入搜索和筛选图标
import searchIcon from '../assets/images/search-icon.svg'
import searchLine from '../assets/images/search-line.svg'
import searchText from '../assets/images/search-text.svg'
import searchButton from '../assets/images/search-button.svg'
import filterIcon from '../assets/images/filter-icon.svg'
import filterDot from '../assets/images/filter-dot.svg'
import filterMain from '../assets/images/filter-main.svg'
import filterElement1 from '../assets/images/filter-element1.svg'
import filterElement2 from '../assets/images/filter-element2.svg'
import filterElement3 from '../assets/images/filter-element3.svg'
import filterElement4 from '../assets/images/filter-element4.svg'
import filterElement5 from '../assets/images/filter-element5.svg'

// 导入下拉箭头图标
import chevronDown1 from '../assets/images/chevron-down1.svg'
import chevronDown2 from '../assets/images/chevron-down2.svg'
import chevronDown3 from '../assets/images/chevron-down3.svg'

// 导入右箭头图标
import caretRight1 from '../assets/images/caret-right1.svg'
import caretRight2 from '../assets/images/caret-right2.svg'

// 导入底部导航图标
import homeIcon from '../assets/images/home-icon.svg'
import folderIcon from '../assets/images/folder-icon.svg'
import assessmentIcon from '../assets/images/assessment-icon.svg'
import userIcon from '../assets/images/user-icon.svg'

export default {
  name: 'HistoryPage',
  emits: ['switch-tab'],
  data() {
    return {
      // 搜索和筛选图标
      searchIcon,
      searchLine,
      searchText,
      searchButton,
      filterIcon,
      filterDot,
      filterMain,
      filterElement1,
      filterElement2,
      filterElement3,
      filterElement4,
      filterElement5,
      // 下拉箭头图标
      chevronDown1,
      chevronDown2,
      chevronDown3,
      // 右箭头图标
      caretRight1,
      caretRight2,
      // 底部导航图标
      homeIcon,
      folderIcon,
      assessmentIcon,
      userIcon
    }
  },
  methods: {
    switchTab(tab) {
      console.log('切换到:', tab)
      this.$emit('switch-tab', tab)
    }
  }
}
</script>

<style scoped>
.history-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.search-container {
  width: 72px;
  height: 40px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  position: absolute;
  left: 30px;
  top: 16.79px;
  width: 7.86px;
  height: 10.7px;
}

.search-line {
  position: absolute;
  left: 39.59px;
  top: 18.4px;
  width: 2.25px;
  height: 7.47px;
}

.search-text {
  position: absolute;
  left: 43.53px;
  top: 17.04px;
  width: 8.1px;
  height: 10.19px;
}

.search-button {
  position: absolute;
  left: 52.86px;
  top: 17.04px;
  width: 4.48px;
  height: 10.19px;
}

.status-right {
  display: flex;
  align-items: center;
}

.filter-container {
  width: 96px;
  height: 40px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-icon {
  position: absolute;
  left: 55.05px;
  top: 16.67px;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
  stroke: #000000;
  stroke-width: 1px;
}

.filter-dot {
  position: absolute;
  left: 76.68px;
  top: 20.43px;
  width: 1.19px;
  height: 3.59px;
  opacity: 0.4;
}

.filter-main {
  position: absolute;
  left: 56.26px;
  top: 17.87px;
  width: 17.87px;
  height: 7.66px;
}

.filter-element1 {
  position: absolute;
  left: 34.13px;
  top: 17.02px;
  width: 14.47px;
  height: 10.07px;
}

.filter-element2 {
  position: absolute;
  left: 20.51px;
  top: 18.72px;
  width: 2.55px;
  height: 8.51px;
}

.filter-element3 {
  position: absolute;
  left: 24.77px;
  top: 17.02px;
  width: 2.55px;
  height: 10.21px;
}

.filter-element4 {
  position: absolute;
  left: 16.26px;
  top: 21.7px;
  width: 2.55px;
  height: 5.53px;
}

.filter-element5 {
  position: absolute;
  left: 12px;
  top: 23.83px;
  width: 2.55px;
  height: 3.4px;
}

/* 筛选器区域 */
.filter-section {
  padding: 10px 23px 11px 23px;
  display: flex;
  gap: 9px;
  background: #FFFFFF;
}

.dropdown-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFFFFF;
  border: 1px solid #BCC1CA;
  border-radius: 4px;
  padding: 5px 8px;
  cursor: pointer;
}

.dropdown-button:first-child {
  width: 156px;
  height: 27px;
}

.dropdown-button:nth-child(2) {
  width: 77px;
  height: 27px;
}

.dropdown-button:nth-child(3) {
  width: 77px;
  height: 27px;
}

.dropdown-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #171A1F;
}

.chevron-icon {
  width: 8.28px;
  height: 4.88px;
  margin-left: 8px;
}

/* 简洁样式的下拉按钮 */
.dropdown-button.simple-style {
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  padding: 8px 12px;
  min-width: 80px;
  height: auto;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.dropdown-button.simple-style:hover {
  border-color: #D1D5DB;
  background: #F9FAFB;
}

.dropdown-button.simple-style .dropdown-text {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.dropdown-button.simple-style .chevron-icon {
  width: 12px;
  height: 12px;
  margin-left: 6px;
  opacity: 0.6;
}

/* 作文列表容器 */
.essay-list-container {
  background: #FFFFFF;
}

/* 学年标题 */
.academic-year-header {
  background: #F8F9FA;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18px;
}

.year-title {
  font-family: Inter;
  font-weight: 700;
  font-size: 12px;
  line-height: 20px;
  color: #171A1F;
}

.essay-count {
  font-family: Inter;
  font-weight: 700;
  font-size: 12px;
  line-height: 20px;
  color: #171A1F;
}

/* 作文组 */
.essay-group {
  background: #FFFFFF;
}

.essay-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 11px 17px;
  height: 94px;
  box-sizing: border-box;
}

.essay-content {
  flex: 1;
}

.essay-tags {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.unit-tag, .type-tag, .category-tag {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.essay-title {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  margin-bottom: 17px;
}

.essay-time {
  font-family: Inter;
  font-weight: 400;
  font-size: 8px;
  line-height: 14px;
  color: #9095A0;
}

.essay-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.essay-score {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.score-number {
  font-family: Inter;
  font-weight: 400;
  font-size: 24px;
  line-height: 36px;
  color: #DE3B40;
}

.score-unit {
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #DE3B40;
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

.arrow-icon img {
  width: 5.76px;
  height: 10.56px;
}

/* 分隔线 */
.separator {
  height: 20px;
  background: #F8F9FA;
}

/* 底线 */
.bottom-line {
  text-align: center;
  padding: 14px 0;
  font-family: Inter;
  font-weight: 400;
  font-size: 8px;
  line-height: 14px;
  color: #9095A0;
  background: #FFFFFF;
  margin-top: 4px;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 375px;
  height: 48px;
  background: #FFFFFF;
  border-top: 1px solid #F3F4F6;
  display: flex;
  padding: 0 6px;
  box-sizing: border-box;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.tab-text {
  font-size: 10px;
  line-height: 16px;
  color: #424955;
}

.tab-item.active .tab-text {
  font-weight: 700;
  color: #636AE8;
}

.tab-indicator {
  position: absolute;
  bottom: 2.8px;
  width: 12px;
  height: 12px;
  background: #DE3B40;
  border: 2px solid #FFFFFF;
  border-radius: 5px;
}
</style>
